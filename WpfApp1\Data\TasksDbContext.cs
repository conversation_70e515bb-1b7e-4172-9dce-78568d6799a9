using Microsoft.EntityFrameworkCore;
using System.Configuration;
using WpfApp1.Models;

namespace WpfApp1.Data
{
    public class TasksDbContext : DbContext
    {
        public DbSet<TaskModel> Tasks { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                var connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString;
                if (string.IsNullOrEmpty(connectionString))
                {
                    connectionString = "Data Source=.\\sqlexpress;Initial Catalog=databasetasks;Integrated Security=True;Encrypt=False;Trust Server Certificate=True";
                }
                optionsBuilder.UseSqlServer(connectionString);
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // تكوين جدول المهام
            modelBuilder.Entity<TaskModel>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                
                entity.Property(e => e.Title)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.Description)
                    .HasMaxLength(1000);

                entity.Property(e => e.Priority)
                    .HasMaxLength(50)
                    .HasDefaultValue("Medium");

                entity.Property(e => e.Category)
                    .HasMaxLength(50);

                entity.Property(e => e.AssignedTo)
                    .HasMaxLength(100);

                entity.Property(e => e.Status)
                    .HasMaxLength(20)
                    .HasDefaultValue("Pending");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("GETDATE()");

                entity.Property(e => e.IsCompleted)
                    .HasDefaultValue(false);

                entity.Property(e => e.IsAllDay)
                    .HasDefaultValue(false);
            });

            // إضافة بيانات تجريبية
            modelBuilder.Entity<TaskModel>().HasData(
                new TaskModel
                {
                    Id = 1,
                    Title = "مهمة تجريبية",
                    Description = "هذه مهمة تجريبية لاختبار النظام",
                    StartDate = DateTime.Now,
                    EndDate = DateTime.Now.AddHours(2),
                    Priority = "High",
                    Category = "Work",
                    Status = "Pending",
                    CreatedAt = DateTime.Now
                }
            );
        }
    }
}
