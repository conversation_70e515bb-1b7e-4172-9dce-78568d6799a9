﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;C:\Program Files\DevExpress 24.2\Components\Offline Packages;C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.0</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="C:\Program Files\DevExpress 24.2\Components\Offline Packages\" />
    <SourceRoot Include="C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages\" />
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.entityframeworkcore\9.0.7\buildTransitive\net8.0\Microsoft.EntityFrameworkCore.props" Condition="Exists('$(NuGetPackageRoot)microsoft.entityframeworkcore\9.0.7\buildTransitive\net8.0\Microsoft.EntityFrameworkCore.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.codeanalysis.analyzers\3.3.4\buildTransitive\Microsoft.CodeAnalysis.Analyzers.props" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.analyzers\3.3.4\buildTransitive\Microsoft.CodeAnalysis.Analyzers.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.entityframeworkcore.design\9.0.7\build\net8.0\Microsoft.EntityFrameworkCore.Design.props" Condition="Exists('$(NuGetPackageRoot)microsoft.entityframeworkcore.design\9.0.7\build\net8.0\Microsoft.EntityFrameworkCore.Design.props')" />
    <Import Project="C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.data.desktop\24.2.3\build\net8.0-windows\DevExpress.Data.Desktop.props" Condition="Exists('C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.data.desktop\24.2.3\build\net8.0-windows\DevExpress.Data.Desktop.props')" />
    <Import Project="C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.core\24.2.3\build\net8.0-windows\DevExpress.Wpf.Core.props" Condition="Exists('C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.core\24.2.3\build\net8.0-windows\DevExpress.Wpf.Core.props')" />
  </ImportGroup>
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <PkgMicrosoft_CodeAnalysis_Analyzers Condition=" '$(PkgMicrosoft_CodeAnalysis_Analyzers)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4</PkgMicrosoft_CodeAnalysis_Analyzers>
    <PkgMicrosoft_EntityFrameworkCore_Tools Condition=" '$(PkgMicrosoft_EntityFrameworkCore_Tools)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.entityframeworkcore.tools\9.0.7</PkgMicrosoft_EntityFrameworkCore_Tools>
    <PkgDevExpress_Data Condition=" '$(PkgDevExpress_Data)' == '' ">C:\Users\<USER>\.nuget\packages\devexpress.data\24.2.3</PkgDevExpress_Data>
    <PkgDevExpress_Wpf_Core Condition=" '$(PkgDevExpress_Wpf_Core)' == '' ">C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.core\24.2.3</PkgDevExpress_Wpf_Core>
    <PkgDevExpress_Wpf_Ribbon Condition=" '$(PkgDevExpress_Wpf_Ribbon)' == '' ">C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.ribbon\24.2.3</PkgDevExpress_Wpf_Ribbon>
    <PkgDevExpress_Wpf_LayoutControl Condition=" '$(PkgDevExpress_Wpf_LayoutControl)' == '' ">C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.layoutcontrol\24.2.3</PkgDevExpress_Wpf_LayoutControl>
    <PkgDevExpress_Wpf_Grid_Core Condition=" '$(PkgDevExpress_Wpf_Grid_Core)' == '' ">C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.grid.core\24.2.3</PkgDevExpress_Wpf_Grid_Core>
    <PkgDevExpress_Wpf_Controls Condition=" '$(PkgDevExpress_Wpf_Controls)' == '' ">C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.controls\24.2.3</PkgDevExpress_Wpf_Controls>
    <PkgDevExpress_Wpf_DocumentViewer_Core Condition=" '$(PkgDevExpress_Wpf_DocumentViewer_Core)' == '' ">C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.documentviewer.core\24.2.3</PkgDevExpress_Wpf_DocumentViewer_Core>
    <PkgDevExpress_Wpf_Docking Condition=" '$(PkgDevExpress_Wpf_Docking)' == '' ">C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.docking\24.2.3</PkgDevExpress_Wpf_Docking>
    <PkgDevExpress_Wpf_Printing Condition=" '$(PkgDevExpress_Wpf_Printing)' == '' ">C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.printing\24.2.3</PkgDevExpress_Wpf_Printing>
    <PkgDevExpress_Wpf_Scheduling Condition=" '$(PkgDevExpress_Wpf_Scheduling)' == '' ">C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.scheduling\24.2.3</PkgDevExpress_Wpf_Scheduling>
  </PropertyGroup>
</Project>