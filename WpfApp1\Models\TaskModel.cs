using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WpfApp1.Models
{
    [Table("Tasks")]
    public class TaskModel
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Description { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }

        public bool IsCompleted { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        [StringLength(50)]
        public string? Priority { get; set; } = "Medium";

        [StringLength(50)]
        public string? Category { get; set; }

        [StringLength(100)]
        public string? AssignedTo { get; set; }

        public bool IsAllDay { get; set; } = false;

        [StringLength(20)]
        public string? Status { get; set; } = "Pending";
    }
}
