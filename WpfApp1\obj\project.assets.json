{"version": 3, "targets": {"net9.0-windows7.0": {"DevExpress.Data/24.2.3": {"type": "package", "dependencies": {"System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.Data.v24.2.dll": {"related": ".dll.DFX;.xml"}}, "runtime": {"lib/net8.0/DevExpress.Data.v24.2.dll": {"related": ".dll.DFX;.xml"}}}, "DevExpress.Data.Desktop/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "System.Data.OleDb": "8.0.1"}, "compile": {"lib/net8.0-windows/DevExpress.Data.Desktop.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Data.Desktop.v24.2.dll": {"related": ".xml"}}, "build": {"build/net8.0-windows/DevExpress.Data.Desktop.props": {}}}, "DevExpress.Drawing/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.Drawing.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Drawing.v24.2.dll": {"related": ".xml"}}}, "DevExpress.Images/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.Images.v24.2.dll": {}}, "runtime": {"lib/net8.0/DevExpress.Images.v24.2.dll": {}}}, "DevExpress.Mvvm/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Mvvm.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Mvvm.v24.2.dll": {"related": ".xml"}}}, "DevExpress.Office.Core/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "DevExpress.Pdf.Core": "[24.2.3]", "DevExpress.Printing.Core": "[24.2.3]", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.Office.v24.2.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Office.v24.2.Core.dll": {"related": ".xml"}}}, "DevExpress.Pdf.Core/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "System.Drawing.Common": "4.7.2", "System.Security.Cryptography.Pkcs": "8.0.1"}, "compile": {"lib/net8.0/DevExpress.Pdf.v24.2.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Pdf.v24.2.Core.dll": {"related": ".xml"}}}, "DevExpress.Pdf.Drawing/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "DevExpress.Pdf.Core": "[24.2.3]", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.Pdf.v24.2.Drawing.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Pdf.v24.2.Drawing.dll": {"related": ".xml"}}}, "DevExpress.Printing.Core/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "DevExpress.Pdf.Core": "[24.2.3]", "DevExpress.Pdf.Drawing": "[24.2.3]", "System.Drawing.Common": "4.7.2", "System.Security.Cryptography.Pkcs": "8.0.1", "System.ServiceModel.Http": "6.2.0"}, "compile": {"lib/net8.0/DevExpress.Printing.v24.2.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Printing.v24.2.Core.dll": {"related": ".xml"}}}, "DevExpress.RichEdit.Core/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "DevExpress.Office.Core": "[24.2.3]", "DevExpress.Pdf.Core": "[24.2.3]", "DevExpress.Printing.Core": "[24.2.3]", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.RichEdit.v24.2.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.RichEdit.v24.2.Core.dll": {"related": ".xml"}}}, "DevExpress.Scheduler.Core/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.XtraScheduler.v24.2.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.XtraScheduler.v24.2.Core.dll": {"related": ".xml"}}}, "DevExpress.Scheduler.CoreDesktop/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Data.Desktop": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "DevExpress.Images": "[24.2.3]", "DevExpress.Printing.Core": "[24.2.3]", "DevExpress.Scheduler.Core": "[24.2.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraScheduler.v24.2.Core.Desktop.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraScheduler.v24.2.Core.Desktop.dll": {"related": ".xml"}}}, "DevExpress.Wpf.Controls/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Data.Desktop": "[24.2.3]", "DevExpress.Mvvm": "[24.2.3]", "DevExpress.Printing.Core": "[24.2.3]", "DevExpress.Wpf.Core": "[24.2.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Xpf.Controls.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Controls.v24.2.dll": {"related": ".xml"}}}, "DevExpress.Wpf.Core/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Data.Desktop": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "DevExpress.Mvvm": "[24.2.3]", "DevExpress.Pdf.Core": "[24.2.3]", "DevExpress.Printing.Core": "[24.2.3]", "DevExpress.Wpf.Themes.Office2019Colorful": "[24.2.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Xpf.Core.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Core.v24.2.dll": {"related": ".xml"}}, "build": {"build/net8.0-windows/DevExpress.Wpf.Core.props": {}}}, "DevExpress.Wpf.Docking/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Data.Desktop": "[24.2.3]", "DevExpress.Mvvm": "[24.2.3]", "DevExpress.Wpf.Core": "[24.2.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Xpf.Docking.v24.2.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.Xpf.Layout.v24.2.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Docking.v24.2.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.Xpf.Layout.v24.2.Core.dll": {"related": ".xml"}}}, "DevExpress.Wpf.DocumentViewer.Core/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Mvvm": "[24.2.3]", "DevExpress.Wpf.Controls": "[24.2.3]", "DevExpress.Wpf.Core": "[24.2.3]", "DevExpress.Wpf.Grid.Core": "[24.2.3]", "DevExpress.Wpf.Ribbon": "[24.2.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Xpf.DocumentViewer.v24.2.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.DocumentViewer.v24.2.Core.dll": {"related": ".xml"}}}, "DevExpress.Wpf.Grid.Core/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Data.Desktop": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "DevExpress.Mvvm": "[24.2.3]", "DevExpress.Printing.Core": "[24.2.3]", "DevExpress.Wpf.Core": "[24.2.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.Core.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.Extensions.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.dll": {"related": ".Core.xml;.Extensions.xml;.xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.Core.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.Extensions.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.dll": {"related": ".Core.xml;.Extensions.xml;.xml"}}}, "DevExpress.Wpf.LayoutControl/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Data.Desktop": "[24.2.3]", "DevExpress.Mvvm": "[24.2.3]", "DevExpress.Wpf.Core": "[24.2.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Xpf.LayoutControl.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.LayoutControl.v24.2.dll": {"related": ".xml"}}}, "DevExpress.Wpf.Printing/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Data.Desktop": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "DevExpress.Mvvm": "[24.2.3]", "DevExpress.Office.Core": "[24.2.3]", "DevExpress.Printing.Core": "[24.2.3]", "DevExpress.RichEdit.Core": "[24.2.3]", "DevExpress.Wpf.Controls": "[24.2.3]", "DevExpress.Wpf.Core": "[24.2.3]", "DevExpress.Wpf.Docking": "[24.2.3]", "DevExpress.Wpf.DocumentViewer.Core": "[24.2.3]", "DevExpress.Wpf.Grid.Core": "[24.2.3]", "DevExpress.Wpf.LayoutControl": "[24.2.3]", "DevExpress.Wpf.Ribbon": "[24.2.3]", "System.ServiceModel.Http": "8.0.0"}, "compile": {"lib/net8.0-windows/DevExpress.Xpf.Printing.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Printing.v24.2.dll": {"related": ".xml"}}}, "DevExpress.Wpf.Ribbon/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Data.Desktop": "[24.2.3]", "DevExpress.Mvvm": "[24.2.3]", "DevExpress.Wpf.Core": "[24.2.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Xpf.Ribbon.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Ribbon.v24.2.dll": {"related": ".xml"}}}, "DevExpress.Wpf.Scheduling/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Data.Desktop": "[24.2.3]", "DevExpress.Images": "[24.2.3]", "DevExpress.Mvvm": "[24.2.3]", "DevExpress.Printing.Core": "[24.2.3]", "DevExpress.Scheduler.Core": "[24.2.3]", "DevExpress.Scheduler.CoreDesktop": "[24.2.3]", "DevExpress.Wpf.Core": "[24.2.3]", "DevExpress.Wpf.Grid.Core": "[24.2.3]", "DevExpress.Wpf.LayoutControl": "[24.2.3]", "DevExpress.Wpf.Printing": "[24.2.3]", "DevExpress.Wpf.Ribbon": "[24.2.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Xpf.Scheduling.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Scheduling.v24.2.dll": {"related": ".xml"}}}, "DevExpress.Wpf.Themes.Office2019Colorful/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2019Colorful.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2019Colorful.v24.2.dll": {}}}, "Microsoft.Extensions.ObjectPool/6.0.16": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/3.1.4": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.SystemEvents/4.7.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Configuration.ConfigurationManager/8.0.1": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "8.0.1", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "compile": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Data.OleDb/8.0.1": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "8.0.1", "System.Diagnostics.PerformanceCounter": "8.0.1"}, "compile": {"lib/net8.0/System.Data.OleDb.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Data.OleDb.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Data.OleDb.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.EventLog/8.0.1": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.PerformanceCounter/8.0.1": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "8.0.1"}, "compile": {"lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Drawing.Common/4.7.2": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.Win32.SystemEvents": "4.7.0"}, "compile": {"ref/netcoreapp3.0/System.Drawing.Common.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Drawing.Common.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.AccessControl/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "compile": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.Cryptography.Xml/6.0.1": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Cryptography.Pkcs": "6.0.1"}, "compile": {"lib/net6.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.ServiceModel.Http/8.0.0": {"type": "package", "dependencies": {"System.ServiceModel.Primitives": "8.0.0"}, "compile": {"lib/net8.0/System.ServiceModel.Http.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/System.ServiceModel.Http.dll": {"related": ".pdb"}}, "resource": {"lib/net8.0/cs/System.ServiceModel.Http.resources.dll": {"locale": "cs"}, "lib/net8.0/de/System.ServiceModel.Http.resources.dll": {"locale": "de"}, "lib/net8.0/es/System.ServiceModel.Http.resources.dll": {"locale": "es"}, "lib/net8.0/fr/System.ServiceModel.Http.resources.dll": {"locale": "fr"}, "lib/net8.0/it/System.ServiceModel.Http.resources.dll": {"locale": "it"}, "lib/net8.0/ja/System.ServiceModel.Http.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/System.ServiceModel.Http.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/System.ServiceModel.Http.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/System.ServiceModel.Http.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/System.ServiceModel.Http.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/System.ServiceModel.Http.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/System.ServiceModel.Http.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/System.ServiceModel.Http.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.ServiceModel.Primitives/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.ObjectPool": "6.0.16", "System.Security.Cryptography.Xml": "6.0.1"}, "compile": {"ref/net8.0/System.ServiceModel.Primitives.dll": {}}, "runtime": {"lib/net8.0/System.ServiceModel.Primitives.dll": {"related": ".pdb"}}, "resource": {"lib/net8.0/cs/System.ServiceModel.Primitives.resources.dll": {"locale": "cs"}, "lib/net8.0/de/System.ServiceModel.Primitives.resources.dll": {"locale": "de"}, "lib/net8.0/es/System.ServiceModel.Primitives.resources.dll": {"locale": "es"}, "lib/net8.0/fr/System.ServiceModel.Primitives.resources.dll": {"locale": "fr"}, "lib/net8.0/it/System.ServiceModel.Primitives.resources.dll": {"locale": "it"}, "lib/net8.0/ja/System.ServiceModel.Primitives.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/System.ServiceModel.Primitives.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/System.ServiceModel.Primitives.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/System.ServiceModel.Primitives.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/System.ServiceModel.Primitives.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/System.ServiceModel.Primitives.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/System.ServiceModel.Primitives.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/System.ServiceModel.Primitives.resources.dll": {"locale": "zh-Han<PERSON>"}}}}}, "libraries": {"DevExpress.Data/24.2.3": {"sha512": "B35Z03dwKg5WCgmFNwz1gfOTubIsneMi+koh17iLHutKP1YpfvoR2hZNXDtuQqE7eL/HSuptTpwsvNtt+udQcw==", "type": "package", "path": "devexpress.data/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/DevExpress.Generator.dll", "devexpress.data.24.2.3.nupkg.sha512", "devexpress.data.nuspec", "lib/net462/DevExpress.Data.v24.2.dll", "lib/net462/DevExpress.Data.v24.2.dll.DFX", "lib/net462/DevExpress.Data.v24.2.xml", "lib/net8.0/DevExpress.Data.v24.2.dll", "lib/net8.0/DevExpress.Data.v24.2.dll.DFX", "lib/net8.0/DevExpress.Data.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Data.Desktop/24.2.3": {"sha512": "L9bDiEpPfSZhyjRdElmrbaVG8RbsVKFnuX76/jM0IcEr4PpeG7vsgvFUXdJuvb3NeXAOTVwJmqXyujrkJJKgNQ==", "type": "package", "path": "devexpress.data.desktop/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "build/net8.0-windows/DevExpress.Data.Desktop.props", "devexpress.data.desktop.24.2.3.nupkg.sha512", "devexpress.data.desktop.nuspec", "lib/net462/DevExpress.Data.Desktop.v24.2.dll", "lib/net462/DevExpress.Data.Desktop.v24.2.xml", "lib/net8.0-windows/DevExpress.Data.Desktop.v24.2.dll", "lib/net8.0-windows/DevExpress.Data.Desktop.v24.2.xml"]}, "DevExpress.Drawing/24.2.3": {"sha512": "ks/fg5GMeZnRKxghlnxw433IslBXJNzG5xRxEj8NMSVF7xCJFjbOdJ+rSeZ00Hca/faAEcKT+Cahx6xTAdsbRg==", "type": "package", "path": "devexpress.drawing/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.drawing.24.2.3.nupkg.sha512", "devexpress.drawing.nuspec", "lib/net462/DevExpress.Drawing.v24.2.dll", "lib/net462/DevExpress.Drawing.v24.2.xml", "lib/net8.0/DevExpress.Drawing.v24.2.dll", "lib/net8.0/DevExpress.Drawing.v24.2.xml"]}, "DevExpress.Images/24.2.3": {"sha512": "CCBfd0PI65GrgQzub6kHYsxA5CJwHq3zyOGllqv11ddp5vljy1FOOkrJkz80cBFTjiOm+PjOeVy7y+rbSs+Hfg==", "type": "package", "path": "devexpress.images/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.images.24.2.3.nupkg.sha512", "devexpress.images.nuspec", "lib/net462/DevExpress.Images.v24.2.dll", "lib/net8.0/DevExpress.Images.v24.2.dll"]}, "DevExpress.Mvvm/24.2.3": {"sha512": "M+Bm0Ts2rhv1rdtVVnujTdZOzg55rLzGzgc06JTnqx/0RbmhNaX4TUyifReMfFgz0ljqtbWBM0c6HxGrCaFTHA==", "type": "package", "path": "devexpress.mvvm/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.mvvm.24.2.3.nupkg.sha512", "devexpress.mvvm.nuspec", "lib/net462/DevExpress.Mvvm.v24.2.dll", "lib/net462/DevExpress.Mvvm.v24.2.xml", "lib/net8.0-windows/DevExpress.Mvvm.v24.2.dll", "lib/net8.0-windows/DevExpress.Mvvm.v24.2.xml"]}, "DevExpress.Office.Core/24.2.3": {"sha512": "Q5RKL6HmlvgpeWVaV/iA5VlADcmWGAN9V/P9DGeYdvYMBq2KVHeTZ3ReYSKqePd6dg4zqToMv1+sMW42Az03bw==", "type": "package", "path": "devexpress.office.core/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.office.core.24.2.3.nupkg.sha512", "devexpress.office.core.nuspec", "lib/net462/DevExpress.Office.v24.2.Core.dll", "lib/net462/DevExpress.Office.v24.2.Core.xml", "lib/net8.0/DevExpress.Office.v24.2.Core.dll", "lib/net8.0/DevExpress.Office.v24.2.Core.xml"]}, "DevExpress.Pdf.Core/24.2.3": {"sha512": "OT7SVpBhurLC1igYcbq6HP1QsSfV9HosrcBh18AkxYLCiQlzTbCMqApE8iE4vVS9re73ikWV/5g5guEYODPj9Q==", "type": "package", "path": "devexpress.pdf.core/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.pdf.core.24.2.3.nupkg.sha512", "devexpress.pdf.core.nuspec", "lib/net462/DevExpress.Pdf.v24.2.Core.dll", "lib/net462/DevExpress.Pdf.v24.2.Core.xml", "lib/net8.0/DevExpress.Pdf.v24.2.Core.dll", "lib/net8.0/DevExpress.Pdf.v24.2.Core.xml"]}, "DevExpress.Pdf.Drawing/24.2.3": {"sha512": "fHg5SzHeEXvcmM0hrfWzUNEy9WIshPc6TPi3U8GwvPWdS24RargWxSdtrVzeH14+YDKgFbHPA/o2vlYqdS+PeQ==", "type": "package", "path": "devexpress.pdf.drawing/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.pdf.drawing.24.2.3.nupkg.sha512", "devexpress.pdf.drawing.nuspec", "lib/net462/DevExpress.Pdf.v24.2.Drawing.dll", "lib/net462/DevExpress.Pdf.v24.2.Drawing.xml", "lib/net8.0/DevExpress.Pdf.v24.2.Drawing.dll", "lib/net8.0/DevExpress.Pdf.v24.2.Drawing.xml"]}, "DevExpress.Printing.Core/24.2.3": {"sha512": "/EB0Wr1ag7zdi7JmIQPHRXSb2pmj7X8DW2/S72x7u4nkChfTpMFSCZrYOw6EnJ1l8IbZHyeFpsWFiKgJtCPRUA==", "type": "package", "path": "devexpress.printing.core/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.printing.core.24.2.3.nupkg.sha512", "devexpress.printing.core.nuspec", "lib/net462/DevExpress.Printing.v24.2.Core.dll", "lib/net462/DevExpress.Printing.v24.2.Core.xml", "lib/net8.0/DevExpress.Printing.v24.2.Core.dll", "lib/net8.0/DevExpress.Printing.v24.2.Core.xml"]}, "DevExpress.RichEdit.Core/24.2.3": {"sha512": "lj2qJVSkEj+zW4G6gUCFzIo6TSIoN5+o6+oe0UIvlCMeb0rx3X/mDaQg4TXcK/y3FXQZk+DI+BY4mEzaFkXHwg==", "type": "package", "path": "devexpress.richedit.core/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.richedit.core.24.2.3.nupkg.sha512", "devexpress.richedit.core.nuspec", "lib/net462/DevExpress.RichEdit.v24.2.Core.dll", "lib/net462/DevExpress.RichEdit.v24.2.Core.xml", "lib/net8.0/DevExpress.RichEdit.v24.2.Core.dll", "lib/net8.0/DevExpress.RichEdit.v24.2.Core.xml"]}, "DevExpress.Scheduler.Core/24.2.3": {"sha512": "Cu783P74nR6o7Cz7xphauzNJPdrzwvmsHCPvtlE7aqKwxqnFNzs3uAuXZvOcyBmMvB9bXwdYQL8gLJ2yTi/SiA==", "type": "package", "path": "devexpress.scheduler.core/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.scheduler.core.24.2.3.nupkg.sha512", "devexpress.scheduler.core.nuspec", "lib/net462/DevExpress.XtraScheduler.v24.2.Core.dll", "lib/net462/DevExpress.XtraScheduler.v24.2.Core.xml", "lib/net8.0/DevExpress.XtraScheduler.v24.2.Core.dll", "lib/net8.0/DevExpress.XtraScheduler.v24.2.Core.xml"]}, "DevExpress.Scheduler.CoreDesktop/24.2.3": {"sha512": "YvYZnSy2nA7rOcmZ36BUcO2YTL0tezLgllrhpprFQqp+4ZHKlcsQ5y6b6G2S9qWSi3yf7RVxmm4EQJS0ogdUTg==", "type": "package", "path": "devexpress.scheduler.coredesktop/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.scheduler.coredesktop.24.2.3.nupkg.sha512", "devexpress.scheduler.coredesktop.nuspec", "lib/net462/DevExpress.XtraScheduler.v24.2.Core.Desktop.dll", "lib/net462/DevExpress.XtraScheduler.v24.2.Core.Desktop.xml", "lib/net8.0-windows/DevExpress.XtraScheduler.v24.2.Core.Desktop.dll", "lib/net8.0-windows/DevExpress.XtraScheduler.v24.2.Core.Desktop.xml"]}, "DevExpress.Wpf.Controls/24.2.3": {"sha512": "6+6IV55404/1ATYQfzbM5xAdTpCxxLz/GbHe6MG42vPh+vkeOCskbB4Fa3XLLv9KeMf2WMIBDLKPy1mYHTRZ7w==", "type": "package", "path": "devexpress.wpf.controls/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.controls.24.2.3.nupkg.sha512", "devexpress.wpf.controls.nuspec", "lib/net462/Design/DevExpress.Xpf.Controls.v24.2.Design.dll", "lib/net462/Design/DevExpress.Xpf.Controls.v24.2.DesignTools.dll", "lib/net462/DevExpress.Xpf.Controls.v24.2.dll", "lib/net462/DevExpress.Xpf.Controls.v24.2.xml", "lib/net8.0-windows/Design/DevExpress.Xpf.Controls.v24.2.DesignTools.dll", "lib/net8.0-windows/DevExpress.Xpf.Controls.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Controls.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.Core/24.2.3": {"sha512": "VavHwmSoSYKFty33jEYi4TGFeZJnMG8jjU6yWe6v32zQOKniTlHxG/J1Pnhi+U1ukQN5gppGlVHxxu8vGjvMsg==", "type": "package", "path": "devexpress.wpf.core/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/net8.0-windows/DevExpress.Wpf.Core.props", "devexpress.wpf.core.24.2.3.nupkg.sha512", "devexpress.wpf.core.nuspec", "lib/net462/Design/DevExpress.Design.v24.2.dll", "lib/net462/Design/DevExpress.Images.v24.2.dll", "lib/net462/Design/DevExpress.Utils.v24.2.dll", "lib/net462/Design/DevExpress.VisualStudioInterop.VS2019.v24.2.dll", "lib/net462/Design/DevExpress.VisualStudioInterop.v24.2.Base.dll", "lib/net462/Design/DevExpress.Xpf.Core.v24.2.Design.Wizards.dll", "lib/net462/Design/DevExpress.Xpf.Core.v24.2.Design.dll", "lib/net462/Design/DevExpress.Xpf.Core.v24.2.DesignTools.dll", "lib/net462/Design/DevExpress.Xpf.Core.v24.2.DesignTools.dll.config", "lib/net462/Design/DevExpress.Xpf.Themes.VS2019Blue.v24.2.dll", "lib/net462/Design/DevExpress.Xpf.Themes.VS2019Dark.v24.2.dll", "lib/net462/Design/DevExpress.Xpf.Themes.VS2019Light.v24.2.dll", "lib/net462/Design/Server/DesignerRunner.exe", "lib/net462/DevExpress.Xpf.Core.v24.2.dll", "lib/net462/DevExpress.Xpf.Core.v24.2.xml", "lib/net8.0-windows/Design/DevExpress.Xpf.Core.v24.2.DesignTools.dll", "lib/net8.0-windows/Design/DevExpress.Xpf.Core.v24.2.DesignTools.dll.config", "lib/net8.0-windows/Design/Server/DesignerRunner.dll", "lib/net8.0-windows/Design/Server/DesignerRunner.exe", "lib/net8.0-windows/Design/Server/DesignerRunner.runtimeconfig.json", "lib/net8.0-windows/DevExpress.Xpf.Core.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Core.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.Docking/24.2.3": {"sha512": "cTKOOaIv94OmrB8lsfLIBIdmeT6XrDdJ/RvsiTkGAlfVZIJv5Qm9pFvURud8JnQ25uADX3WpEiX5dTi3nD+pqQ==", "type": "package", "path": "devexpress.wpf.docking/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.docking.24.2.3.nupkg.sha512", "devexpress.wpf.docking.nuspec", "lib/net462/Design/DevExpress.Xpf.Docking.v24.2.Design.dll", "lib/net462/Design/DevExpress.Xpf.Docking.v24.2.DesignTools.dll", "lib/net462/DevExpress.Xpf.Docking.v24.2.dll", "lib/net462/DevExpress.Xpf.Docking.v24.2.xml", "lib/net462/DevExpress.Xpf.Layout.v24.2.Core.dll", "lib/net462/DevExpress.Xpf.Layout.v24.2.Core.xml", "lib/net8.0-windows/Design/DevExpress.Xpf.Docking.v24.2.DesignTools.dll", "lib/net8.0-windows/DevExpress.Xpf.Docking.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Docking.v24.2.xml", "lib/net8.0-windows/DevExpress.Xpf.Layout.v24.2.Core.dll", "lib/net8.0-windows/DevExpress.Xpf.Layout.v24.2.Core.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.DocumentViewer.Core/24.2.3": {"sha512": "MZioOx8xuYJMA+ryutZcx7HiWlJTrhVkV9wBD2IK/Gc512HSO3sILgMtgF4x2iD396u82b9aIzOH9GHzucssSQ==", "type": "package", "path": "devexpress.wpf.documentviewer.core/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.documentviewer.core.24.2.3.nupkg.sha512", "devexpress.wpf.documentviewer.core.nuspec", "lib/net462/DevExpress.Xpf.DocumentViewer.v24.2.Core.dll", "lib/net462/DevExpress.Xpf.DocumentViewer.v24.2.Core.xml", "lib/net8.0-windows/DevExpress.Xpf.DocumentViewer.v24.2.Core.dll", "lib/net8.0-windows/DevExpress.Xpf.DocumentViewer.v24.2.Core.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.Grid.Core/24.2.3": {"sha512": "XIQdM2L5jTvNWZ35jGrVgCY8Ze/bGhjD4iqS37ardxi5ztoF8/T5YwxYH1SZ42KlbKIRRLfzy5Mtfd9ABrn+Lg==", "type": "package", "path": "devexpress.wpf.grid.core/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.grid.core.24.2.3.nupkg.sha512", "devexpress.wpf.grid.core.nuspec", "lib/net462/Design/DevExpress.VisualStudioInterop.VS2019.v24.2.dll", "lib/net462/Design/DevExpress.VisualStudioInterop.VS2022.v24.2.dll", "lib/net462/Design/DevExpress.VisualStudioInterop.v24.2.Base.dll", "lib/net462/Design/DevExpress.Xpf.Grid.v24.2.Design.dll", "lib/net462/Design/DevExpress.Xpf.Grid.v24.2.DesignTools.dll", "lib/net462/DevExpress.Xpf.Grid.v24.2.Core.dll", "lib/net462/DevExpress.Xpf.Grid.v24.2.Core.xml", "lib/net462/DevExpress.Xpf.Grid.v24.2.Extensions.dll", "lib/net462/DevExpress.Xpf.Grid.v24.2.Extensions.xml", "lib/net462/DevExpress.Xpf.Grid.v24.2.dll", "lib/net462/DevExpress.Xpf.Grid.v24.2.xml", "lib/net8.0-windows/Design/DevExpress.VisualStudioInterop.VS2022.v24.2.dll", "lib/net8.0-windows/Design/DevExpress.VisualStudioInterop.v24.2.Base.dll", "lib/net8.0-windows/Design/DevExpress.Xpf.Grid.v24.2.DesignTools.dll", "lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.Core.dll", "lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.Core.xml", "lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.Extensions.dll", "lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.Extensions.xml", "lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.LayoutControl/24.2.3": {"sha512": "bo0T0ysMUB5IxNFHYGD/vRg+WPaXaX6oHQ5khn2Nr73PYAh4D1Ke/WHkGTfQk8CEF/EkF1EBLdPVwz0umFgCpg==", "type": "package", "path": "devexpress.wpf.layoutcontrol/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.layoutcontrol.24.2.3.nupkg.sha512", "devexpress.wpf.layoutcontrol.nuspec", "lib/net462/Design/DevExpress.Xpf.LayoutControl.v24.2.Design.dll", "lib/net462/Design/DevExpress.Xpf.LayoutControl.v24.2.DesignTools.dll", "lib/net462/DevExpress.Xpf.LayoutControl.v24.2.dll", "lib/net462/DevExpress.Xpf.LayoutControl.v24.2.xml", "lib/net8.0-windows/Design/DevExpress.Xpf.LayoutControl.v24.2.DesignTools.dll", "lib/net8.0-windows/DevExpress.Xpf.LayoutControl.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.LayoutControl.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.Printing/24.2.3": {"sha512": "s1sUL55Gs7YazfR8bJT+6NyjFSYNfc7bDFZdeHmZEKZtfPlGfHAJ3syCkGnIkGoR2uwiY6eiWtHtIR1yYvXQJA==", "type": "package", "path": "devexpress.wpf.printing/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.printing.24.2.3.nupkg.sha512", "devexpress.wpf.printing.nuspec", "lib/net462/Design/DevExpress.Xpf.Printing.v24.2.Design.dll", "lib/net462/Design/DevExpress.Xpf.Printing.v24.2.DesignTools.dll", "lib/net462/DevExpress.Xpf.Printing.v24.2.dll", "lib/net462/DevExpress.Xpf.Printing.v24.2.xml", "lib/net8.0-windows/Design/DevExpress.Xpf.Printing.v24.2.DesignTools.dll", "lib/net8.0-windows/DevExpress.Xpf.Printing.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Printing.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.Ribbon/24.2.3": {"sha512": "ujkIw62fxVYebmPus+AQC0+8asTmN66KZr/nKFW2fTULU/yxRoOpQQPoHjka6rp/pJGLXBOc7dc57r6TWyP4sg==", "type": "package", "path": "devexpress.wpf.ribbon/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.ribbon.24.2.3.nupkg.sha512", "devexpress.wpf.ribbon.nuspec", "lib/net462/Design/DevExpress.Xpf.Ribbon.v24.2.Design.dll", "lib/net462/Design/DevExpress.Xpf.Ribbon.v24.2.DesignTools.dll", "lib/net462/DevExpress.Xpf.Ribbon.v24.2.dll", "lib/net462/DevExpress.Xpf.Ribbon.v24.2.xml", "lib/net8.0-windows/Design/DevExpress.Xpf.Ribbon.v24.2.DesignTools.dll", "lib/net8.0-windows/DevExpress.Xpf.Ribbon.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Ribbon.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.Scheduling/24.2.3": {"sha512": "lduJeIqrpPwj/qDGoW2pUV7rTeBK36W1v3tzDve4zrS6aYmMic0BFV5jf0kvq/tABwZMAy3DpItcYvKUgkJsOA==", "type": "package", "path": "devexpress.wpf.scheduling/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.scheduling.24.2.3.nupkg.sha512", "devexpress.wpf.scheduling.nuspec", "lib/net462/Design/DevExpress.Xpf.Scheduling.v24.2.Design.dll", "lib/net462/Design/DevExpress.Xpf.Scheduling.v24.2.DesignTools.dll", "lib/net462/DevExpress.Xpf.Scheduling.v24.2.dll", "lib/net462/DevExpress.Xpf.Scheduling.v24.2.xml", "lib/net8.0-windows/Design/DevExpress.Xpf.Scheduling.v24.2.DesignTools.dll", "lib/net8.0-windows/DevExpress.Xpf.Scheduling.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Scheduling.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.Themes.Office2019Colorful/24.2.3": {"sha512": "c76LI6oCSZ0dBlBKg8ysidzxqPEa1m9BAsdYMCT8oneYSrnnenFrDZ5qU9FdPw8XOhCsUvlSc8+JJOxprxEPuw==", "type": "package", "path": "devexpress.wpf.themes.office2019colorful/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.office2019colorful.24.2.3.nupkg.sha512", "devexpress.wpf.themes.office2019colorful.nuspec", "lib/net462/DevExpress.Xpf.Themes.Office2019Colorful.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Office2019Colorful.v24.2.dll"]}, "Microsoft.Extensions.ObjectPool/6.0.16": {"sha512": "OVX5tlKg6LY+XKqlUn7i9KY+6Liut0iewWff2DNr7129i/NJ8rpUzbmxavPydZgcLREEWHklXZiPKCS895tNIQ==", "type": "package", "path": "microsoft.extensions.objectpool/6.0.16", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.ObjectPool.dll", "lib/net461/Microsoft.Extensions.ObjectPool.xml", "lib/net6.0/Microsoft.Extensions.ObjectPool.dll", "lib/net6.0/Microsoft.Extensions.ObjectPool.xml", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.xml", "microsoft.extensions.objectpool.6.0.16.nupkg.sha512", "microsoft.extensions.objectpool.nuspec"]}, "Microsoft.NETCore.Platforms/3.1.4": {"sha512": "9/y05/CuxE+j184Nr4KihhB9KcUkvGojmD4JV4Vt/mHhVZR+eOCD5WCM+CXye9K0OFMsaPXbN+IcaIpjgBGZmg==", "type": "package", "path": "microsoft.netcore.platforms/3.1.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.3.1.4.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.SystemEvents/4.7.0": {"sha512": "mtVirZr++rq+XCDITMUdnETD59XoeMxSpLRIII7JRI6Yj0LEDiO1pPn0ktlnIj12Ix8bfvQqQDMMIF9wC98oCA==", "type": "package", "path": "microsoft.win32.systemevents/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Win32.SystemEvents.dll", "lib/net461/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.4.7.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "ref/net461/Microsoft.Win32.SystemEvents.dll", "ref/net461/Microsoft.Win32.SystemEvents.xml", "ref/net472/Microsoft.Win32.SystemEvents.dll", "ref/net472/Microsoft.Win32.SystemEvents.xml", "ref/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "ref/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/netcoreapp2.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/netcoreapp2.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt", "version.txt"]}, "System.Configuration.ConfigurationManager/8.0.1": {"sha512": "gPYFPDyohW2gXNhdQRSjtmeS6FymL2crg4Sral1wtvEJ7DUqFCDWDVbbLobASbzxfic8U1hQEdC7hmg9LHncMw==", "type": "package", "path": "system.configuration.configurationmanager/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Configuration.ConfigurationManager.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "lib/net462/System.Configuration.ConfigurationManager.dll", "lib/net462/System.Configuration.ConfigurationManager.xml", "lib/net6.0/System.Configuration.ConfigurationManager.dll", "lib/net6.0/System.Configuration.ConfigurationManager.xml", "lib/net7.0/System.Configuration.ConfigurationManager.dll", "lib/net7.0/System.Configuration.ConfigurationManager.xml", "lib/net8.0/System.Configuration.ConfigurationManager.dll", "lib/net8.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.8.0.1.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.OleDb/8.0.1": {"sha512": "RO+/y2ggU5956uQDRXdjA1e2l5yJ4rTWNX76eZ+3sgtYGqGapCe2kQCyiUci+/y6Fyb21Irp4RQEdfrIiuYrxQ==", "type": "package", "path": "system.data.oledb/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Data.OleDb.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Data.OleDb.targets", "lib/net462/System.Data.OleDb.dll", "lib/net462/System.Data.OleDb.xml", "lib/net6.0/System.Data.OleDb.dll", "lib/net6.0/System.Data.OleDb.xml", "lib/net7.0/System.Data.OleDb.dll", "lib/net7.0/System.Data.OleDb.xml", "lib/net8.0/System.Data.OleDb.dll", "lib/net8.0/System.Data.OleDb.xml", "lib/netstandard2.0/System.Data.OleDb.dll", "lib/netstandard2.0/System.Data.OleDb.xml", "runtimes/win/lib/net6.0/System.Data.OleDb.dll", "runtimes/win/lib/net6.0/System.Data.OleDb.xml", "runtimes/win/lib/net7.0/System.Data.OleDb.dll", "runtimes/win/lib/net7.0/System.Data.OleDb.xml", "runtimes/win/lib/net8.0/System.Data.OleDb.dll", "runtimes/win/lib/net8.0/System.Data.OleDb.xml", "system.data.oledb.8.0.1.nupkg.sha512", "system.data.oledb.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.EventLog/8.0.1": {"sha512": "n1ZP7NM2Gkn/MgD8+eOT5MulMj6wfeQMNS2Pizvq5GHCZfjlFMXV2irQlQmJhwA2VABC57M0auudO89Iu2uRLg==", "type": "package", "path": "system.diagnostics.eventlog/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net6.0/System.Diagnostics.EventLog.dll", "lib/net6.0/System.Diagnostics.EventLog.xml", "lib/net7.0/System.Diagnostics.EventLog.dll", "lib/net7.0/System.Diagnostics.EventLog.xml", "lib/net8.0/System.Diagnostics.EventLog.dll", "lib/net8.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.8.0.1.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.PerformanceCounter/8.0.1": {"sha512": "9RfEDiEjlUADeThs8IPdDVTXSnPRSqjfgTQJALpmGFPKC0k2mbdufOXnb/9JZ4I0TkmxOfy3VTJxrHOJSs8cXg==", "type": "package", "path": "system.diagnostics.performancecounter/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.PerformanceCounter.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.PerformanceCounter.targets", "lib/net462/System.Diagnostics.PerformanceCounter.dll", "lib/net462/System.Diagnostics.PerformanceCounter.xml", "lib/net6.0/System.Diagnostics.PerformanceCounter.dll", "lib/net6.0/System.Diagnostics.PerformanceCounter.xml", "lib/net7.0/System.Diagnostics.PerformanceCounter.dll", "lib/net7.0/System.Diagnostics.PerformanceCounter.xml", "lib/net8.0/System.Diagnostics.PerformanceCounter.dll", "lib/net8.0/System.Diagnostics.PerformanceCounter.xml", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.dll", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net7.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net7.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.xml", "system.diagnostics.performancecounter.8.0.1.nupkg.sha512", "system.diagnostics.performancecounter.nuspec", "useSharedDesignerContext.txt"]}, "System.Drawing.Common/4.7.2": {"sha512": "I2y4KBK3VCvU/WqE2xv7NjQ67maXHttkFSHYKgU2evrG9Yqh0oFjfORXt5hZTk+BVjdyFo2h0/YQZsca33BGmg==", "type": "package", "path": "system.drawing.common/4.7.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.Drawing.Common.dll", "ref/netcoreapp3.0/System.Drawing.Common.dll", "ref/netcoreapp3.0/System.Drawing.Common.xml", "ref/netstandard2.0/System.Drawing.Common.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netcoreapp2.0/System.Drawing.Common.dll", "runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.dll", "runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.xml", "runtimes/win/lib/netcoreapp2.0/System.Drawing.Common.dll", "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.dll", "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.xml", "system.drawing.common.4.7.2.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.AccessControl/6.0.0": {"sha512": "AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "type": "package", "path": "system.security.accesscontrol/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.AccessControl.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/net6.0/System.Security.AccessControl.dll", "lib/net6.0/System.Security.AccessControl.xml", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/net6.0/System.Security.AccessControl.dll", "runtimes/win/lib/net6.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.xml", "system.security.accesscontrol.6.0.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Pkcs/8.0.1": {"sha512": "CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA==", "type": "package", "path": "system.security.cryptography.pkcs/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Pkcs.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets", "lib/net462/System.Security.Cryptography.Pkcs.dll", "lib/net462/System.Security.Cryptography.Pkcs.xml", "lib/net6.0/System.Security.Cryptography.Pkcs.dll", "lib/net6.0/System.Security.Cryptography.Pkcs.xml", "lib/net7.0/System.Security.Cryptography.Pkcs.dll", "lib/net7.0/System.Security.Cryptography.Pkcs.xml", "lib/net8.0/System.Security.Cryptography.Pkcs.dll", "lib/net8.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.xml", "system.security.cryptography.pkcs.8.0.1.nupkg.sha512", "system.security.cryptography.pkcs.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.ProtectedData/8.0.0": {"sha512": "+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg==", "type": "package", "path": "system.security.cryptography.protecteddata/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Security.Cryptography.ProtectedData.dll", "lib/net462/System.Security.Cryptography.ProtectedData.xml", "lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "lib/net7.0/System.Security.Cryptography.ProtectedData.dll", "lib/net7.0/System.Security.Cryptography.ProtectedData.xml", "lib/net8.0/System.Security.Cryptography.ProtectedData.dll", "lib/net8.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.security.cryptography.protecteddata.8.0.0.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Xml/6.0.1": {"sha512": "5e5bI28T0x73AwTsbuFP4qSRzthmU2C0Gqgg3AZ3KTxmSyA+Uhk31puA3srdaeWaacVnHhLdJywCzqOiEpbO/w==", "type": "package", "path": "system.security.cryptography.xml/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Xml.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.Cryptography.Xml.dll", "lib/net461/System.Security.Cryptography.Xml.xml", "lib/net6.0/System.Security.Cryptography.Xml.dll", "lib/net6.0/System.Security.Cryptography.Xml.xml", "lib/netstandard2.0/System.Security.Cryptography.Xml.dll", "lib/netstandard2.0/System.Security.Cryptography.Xml.xml", "runtimes/win/lib/net461/System.Security.Cryptography.Xml.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Xml.xml", "system.security.cryptography.xml.6.0.1.nupkg.sha512", "system.security.cryptography.xml.nuspec", "useSharedDesignerContext.txt"]}, "System.ServiceModel.Http/8.0.0": {"sha512": "Qwkoe0F+2e/2LiNwiIgfBTJTw11flv208UwS38ru+GR7nZk2VdGvAE8tqGB0RQIGra73Rux9jKNgfy1XtfXdLg==", "type": "package", "path": "system.servicemodel.http/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0/System.ServiceModel.Http.dll", "lib/net8.0/System.ServiceModel.Http.pdb", "lib/net8.0/cs/System.ServiceModel.Http.resources.dll", "lib/net8.0/de/System.ServiceModel.Http.resources.dll", "lib/net8.0/es/System.ServiceModel.Http.resources.dll", "lib/net8.0/fr/System.ServiceModel.Http.resources.dll", "lib/net8.0/it/System.ServiceModel.Http.resources.dll", "lib/net8.0/ja/System.ServiceModel.Http.resources.dll", "lib/net8.0/ko/System.ServiceModel.Http.resources.dll", "lib/net8.0/pl/System.ServiceModel.Http.resources.dll", "lib/net8.0/pt-BR/System.ServiceModel.Http.resources.dll", "lib/net8.0/ru/System.ServiceModel.Http.resources.dll", "lib/net8.0/tr/System.ServiceModel.Http.resources.dll", "lib/net8.0/zh-Hans/System.ServiceModel.Http.resources.dll", "lib/net8.0/zh-Hant/System.ServiceModel.Http.resources.dll", "system.servicemodel.http.8.0.0.nupkg.sha512", "system.servicemodel.http.nuspec"]}, "System.ServiceModel.Primitives/8.0.0": {"sha512": "hVzK77Bl00H+1V7ho7h03tKlgxAIKssV3eUnRdH+gTCZCK4Ywnv2CR35AV9ly/tRpvsGwNL1d/jkAwB1MWw3Fw==", "type": "package", "path": "system.servicemodel.primitives/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0/System.ServiceModel.Primitives.dll", "lib/net8.0/System.ServiceModel.Primitives.pdb", "lib/net8.0/cs/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/de/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/es/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/fr/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/it/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/ja/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/ko/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/pl/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/pt-BR/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/ru/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/tr/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/zh-Hans/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/zh-Hant/System.ServiceModel.Primitives.resources.dll", "ref/net8.0/System.ServiceModel.Primitives.dll", "system.servicemodel.primitives.8.0.0.nupkg.sha512", "system.servicemodel.primitives.nuspec"]}}, "projectFileDependencyGroups": {"net9.0-windows7.0": ["devexpress.wpf.scheduling >= 24.2.3"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\WpfApp1\\WpfApp1\\WpfApp1.csproj", "projectName": "WpfApp1", "projectPath": "C:\\Users\\<USER>\\source\\repos\\WpfApp1\\WpfApp1\\WpfApp1.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\WpfApp1\\WpfApp1\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 19.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\DevExpress 19.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"devexpress.wpf.scheduling": {"target": "Package", "version": "[24.2.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}