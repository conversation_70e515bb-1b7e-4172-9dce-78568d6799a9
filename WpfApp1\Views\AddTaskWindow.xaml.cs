using System;
using System.Windows;
using System.Windows.Controls;
using WpfApp1.Models;

namespace WpfApp1.Views
{
    /// <summary>
    /// Interaction logic for AddTaskWindow.xaml
    /// </summary>
    public partial class AddTaskWindow : Window
    {
        public TaskModel Task { get; private set; }

        public AddTaskWindow()
        {
            InitializeComponent();
            InitializeForm();
        }

        public AddTaskWindow(TaskModel existingTask) : this()
        {
            Task = existingTask;
            LoadTaskData();
            Title = "تعديل المهمة";
            btnSave.Content = "تحديث";
        }

        private void InitializeForm()
        {
            // تعيين التواريخ الافتراضية
            dpStartDate.SelectedDate = DateTime.Now;
            dpEndDate.SelectedDate = DateTime.Now.AddHours(1);
            
            // تعيين القيم الافتراضية
            cmbPriority.SelectedIndex = 1; // متوسطة
            cmbCategory.SelectedIndex = 0; // عمل
        }

        private void LoadTaskData()
        {
            if (Task == null) return;

            txtTitle.Text = Task.Title;
            txtDescription.Text = Task.Description ?? string.Empty;
            dpStartDate.SelectedDate = Task.StartDate;
            dpEndDate.SelectedDate = Task.EndDate;
            txtAssignedTo.Text = Task.AssignedTo ?? string.Empty;
            chkAllDay.IsChecked = Task.IsAllDay;

            // تعيين الأولوية
            foreach (ComboBoxItem item in cmbPriority.Items)
            {
                if (item.Tag?.ToString() == Task.Priority)
                {
                    cmbPriority.SelectedItem = item;
                    break;
                }
            }

            // تعيين الفئة
            foreach (ComboBoxItem item in cmbCategory.Items)
            {
                if (item.Tag?.ToString() == Task.Category)
                {
                    cmbCategory.SelectedItem = item;
                    break;
                }
            }
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                if (Task == null)
                {
                    Task = new TaskModel();
                }

                Task.Title = txtTitle.Text.Trim();
                Task.Description = string.IsNullOrWhiteSpace(txtDescription.Text) ? null : txtDescription.Text.Trim();
                Task.StartDate = dpStartDate.SelectedDate ?? DateTime.Now;
                Task.EndDate = dpEndDate.SelectedDate ?? DateTime.Now.AddHours(1);
                Task.AssignedTo = string.IsNullOrWhiteSpace(txtAssignedTo.Text) ? null : txtAssignedTo.Text.Trim();
                Task.IsAllDay = chkAllDay.IsChecked ?? false;

                // تعيين الأولوية
                var selectedPriority = cmbPriority.SelectedItem as ComboBoxItem;
                Task.Priority = selectedPriority?.Tag?.ToString() ?? "Medium";

                // تعيين الفئة
                var selectedCategory = cmbCategory.SelectedItem as ComboBoxItem;
                Task.Category = selectedCategory?.Tag?.ToString() ?? "Work";

                // التأكد من صحة التواريخ
                if (Task.EndDate <= Task.StartDate)
                {
                    Task.EndDate = Task.StartDate.AddHours(1);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private bool ValidateInput()
        {
            // التحقق من العنوان
            if (string.IsNullOrWhiteSpace(txtTitle.Text))
            {
                MessageBox.Show("يرجى إدخال عنوان المهمة", "تحقق من البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                txtTitle.Focus();
                return false;
            }

            // التحقق من تاريخ البداية
            if (!dpStartDate.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ البداية", "تحقق من البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                dpStartDate.Focus();
                return false;
            }

            // التحقق من تاريخ النهاية
            if (!dpEndDate.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ النهاية", "تحقق من البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                dpEndDate.Focus();
                return false;
            }

            // التحقق من منطقية التواريخ
            if (dpEndDate.SelectedDate <= dpStartDate.SelectedDate)
            {
                MessageBox.Show("تاريخ النهاية يجب أن يكون بعد تاريخ البداية", "تحقق من البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                dpEndDate.Focus();
                return false;
            }

            return true;
        }
    }
}
