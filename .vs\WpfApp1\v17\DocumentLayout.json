{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\WpfApp1\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{440EB437-2859-4DAB-8819-8046B4DC333B}|WpfApp1\\WpfApp1.csproj|c:\\users\\<USER>\\source\\repos\\wpfapp1\\wpfapp1\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{440EB437-2859-4DAB-8819-8046B4DC333B}|WpfApp1\\WpfApp1.csproj|solutionrelative:wpfapp1\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{440EB437-2859-4DAB-8819-8046B4DC333B}|WpfApp1\\WpfApp1.csproj|c:\\users\\<USER>\\source\\repos\\wpfapp1\\wpfapp1\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{440EB437-2859-4DAB-8819-8046B4DC333B}|WpfApp1\\WpfApp1.csproj|solutionrelative:wpfapp1\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{440EB437-2859-4DAB-8819-8046B4DC333B}|WpfApp1\\WpfApp1.csproj|c:\\users\\<USER>\\source\\repos\\wpfapp1\\wpfapp1\\wpfapp1.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{440EB437-2859-4DAB-8819-8046B4DC333B}|WpfApp1\\WpfApp1.csproj|solutionrelative:wpfapp1\\wpfapp1.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{440EB437-2859-4DAB-8819-8046B4DC333B}|WpfApp1\\WpfApp1.csproj|c:\\users\\<USER>\\source\\repos\\wpfapp1\\wpfapp1\\window1.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{440EB437-2859-4DAB-8819-8046B4DC333B}|WpfApp1\\WpfApp1.csproj|solutionrelative:wpfapp1\\window1.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{440EB437-2859-4DAB-8819-8046B4DC333B}|WpfApp1\\WpfApp1.csproj|c:\\users\\<USER>\\source\\repos\\wpfapp1\\wpfapp1\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{440EB437-2859-4DAB-8819-8046B4DC333B}|WpfApp1\\WpfApp1.csproj|solutionrelative:wpfapp1\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{440EB437-2859-4DAB-8819-8046B4DC333B}|WpfApp1\\WpfApp1.csproj|c:\\users\\<USER>\\source\\repos\\wpfapp1\\wpfapp1\\assemblyinfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{440EB437-2859-4DAB-8819-8046B4DC333B}|WpfApp1\\WpfApp1.csproj|solutionrelative:wpfapp1\\assemblyinfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{440EB437-2859-4DAB-8819-8046B4DC333B}|WpfApp1\\WpfApp1.csproj|c:\\users\\<USER>\\source\\repos\\wpfapp1\\wpfapp1\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{440EB437-2859-4DAB-8819-8046B4DC333B}|WpfApp1\\WpfApp1.csproj|solutionrelative:wpfapp1\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{440EB437-2859-4DAB-8819-8046B4DC333B}|WpfApp1\\WpfApp1.csproj|c:\\users\\<USER>\\source\\repos\\wpfapp1\\wpfapp1\\window1.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{440EB437-2859-4DAB-8819-8046B4DC333B}|WpfApp1\\WpfApp1.csproj|solutionrelative:wpfapp1\\window1.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 398, "SelectedChildIndex": 8, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "App.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\WpfApp1\\WpfApp1\\App.xaml.cs", "RelativeDocumentMoniker": "WpfApp1\\App.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\WpfApp1\\WpfApp1\\App.xaml.cs", "RelativeToolTip": "WpfApp1\\App.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-13T17:32:55.729Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "WpfApp1", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\WpfApp1\\WpfApp1\\WpfApp1.csproj", "RelativeDocumentMoniker": "WpfApp1\\WpfApp1.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\WpfApp1\\WpfApp1\\WpfApp1.csproj", "RelativeToolTip": "WpfApp1\\WpfApp1.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-13T17:25:10.98Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "AssemblyInfo.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\WpfApp1\\WpfApp1\\AssemblyInfo.cs", "RelativeDocumentMoniker": "WpfApp1\\AssemblyInfo.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\WpfApp1\\WpfApp1\\AssemblyInfo.cs", "RelativeToolTip": "WpfApp1\\AssemblyInfo.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-13T16:59:07.7Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "App.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\WpfApp1\\WpfApp1\\App.xaml", "RelativeDocumentMoniker": "WpfApp1\\App.xaml", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\WpfApp1\\WpfApp1\\App.xaml", "RelativeToolTip": "WpfApp1\\App.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-13T16:59:04.091Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "Window1.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\WpfApp1\\WpfApp1\\Window1.xaml.cs", "RelativeDocumentMoniker": "WpfApp1\\Window1.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\WpfApp1\\WpfApp1\\Window1.xaml.cs", "RelativeToolTip": "WpfApp1\\Window1.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-13T16:54:37.217Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "Window1.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\WpfApp1\\WpfApp1\\Window1.xaml", "RelativeDocumentMoniker": "WpfApp1\\Window1.xaml", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\WpfApp1\\WpfApp1\\Window1.xaml", "RelativeToolTip": "WpfApp1\\Window1.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-13T16:54:34.137Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "MainWindow.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\WpfApp1\\WpfApp1\\MainWindow.xaml", "RelativeDocumentMoniker": "WpfApp1\\MainWindow.xaml", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\WpfApp1\\WpfApp1\\MainWindow.xaml", "RelativeToolTip": "WpfApp1\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-13T16:52:47.881Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\WpfApp1\\WpfApp1\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "WpfApp1\\MainWindow.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\WpfApp1\\WpfApp1\\MainWindow.xaml.cs", "RelativeToolTip": "WpfApp1\\MainWindow.xaml.cs", "ViewState": "AgIAADsAAAAAAAAAAAAcwEYAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-13T16:52:43.982Z", "EditorCaption": ""}]}]}]}