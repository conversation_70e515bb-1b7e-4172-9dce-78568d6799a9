using Microsoft.EntityFrameworkCore;
using System.Collections.ObjectModel;
using WpfApp1.Data;
using WpfApp1.Models;

namespace WpfApp1.Services
{
    public class TaskService
    {
        private readonly TasksDbContext _context;

        public TaskService()
        {
            _context = new TasksDbContext();
            // التأكد من إنشاء قاعدة البيانات
            _context.Database.EnsureCreated();
        }

        // الحصول على جميع المهام
        public async Task<ObservableCollection<TaskModel>> GetAllTasksAsync()
        {
            var tasks = await _context.Tasks.ToListAsync();
            return new ObservableCollection<TaskModel>(tasks);
        }

        // إضافة مهمة جديدة
        public async Task<TaskModel> AddTaskAsync(TaskModel task)
        {
            task.CreatedAt = DateTime.Now;
            _context.Tasks.Add(task);
            await _context.SaveChangesAsync();
            return task;
        }

        // تحديث مهمة موجودة
        public async Task<TaskModel> UpdateTaskAsync(TaskModel task)
        {
            task.UpdatedAt = DateTime.Now;
            _context.Tasks.Update(task);
            await _context.SaveChangesAsync();
            return task;
        }

        // حذف مهمة
        public async Task<bool> DeleteTaskAsync(int taskId)
        {
            var task = await _context.Tasks.FindAsync(taskId);
            if (task != null)
            {
                _context.Tasks.Remove(task);
                await _context.SaveChangesAsync();
                return true;
            }
            return false;
        }

        // الحصول على مهمة بالمعرف
        public async Task<TaskModel?> GetTaskByIdAsync(int id)
        {
            return await _context.Tasks.FindAsync(id);
        }

        // الحصول على المهام حسب التاريخ
        public async Task<ObservableCollection<TaskModel>> GetTasksByDateAsync(DateTime date)
        {
            var tasks = await _context.Tasks
                .Where(t => t.StartDate.Date <= date.Date && t.EndDate.Date >= date.Date)
                .ToListAsync();
            return new ObservableCollection<TaskModel>(tasks);
        }

        // الحصول على المهام المكتملة
        public async Task<ObservableCollection<TaskModel>> GetCompletedTasksAsync()
        {
            var tasks = await _context.Tasks
                .Where(t => t.IsCompleted)
                .ToListAsync();
            return new ObservableCollection<TaskModel>(tasks);
        }

        // الحصول على المهام المعلقة
        public async Task<ObservableCollection<TaskModel>> GetPendingTasksAsync()
        {
            var tasks = await _context.Tasks
                .Where(t => !t.IsCompleted)
                .ToListAsync();
            return new ObservableCollection<TaskModel>(tasks);
        }

        // تحديث حالة المهمة
        public async Task<bool> UpdateTaskStatusAsync(int taskId, bool isCompleted)
        {
            var task = await _context.Tasks.FindAsync(taskId);
            if (task != null)
            {
                task.IsCompleted = isCompleted;
                task.Status = isCompleted ? "Completed" : "Pending";
                task.UpdatedAt = DateTime.Now;
                await _context.SaveChangesAsync();
                return true;
            }
            return false;
        }

        // البحث في المهام
        public async Task<ObservableCollection<TaskModel>> SearchTasksAsync(string searchTerm)
        {
            var tasks = await _context.Tasks
                .Where(t => t.Title.Contains(searchTerm) || 
                           (t.Description != null && t.Description.Contains(searchTerm)))
                .ToListAsync();
            return new ObservableCollection<TaskModel>(tasks);
        }

        // تحرير الموارد
        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
