using System.Collections.ObjectModel;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using WpfApp1.Models;
using WpfApp1.Services;
using WpfApp1.Views;

namespace WpfApp1
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly TaskService _taskService;
        private ObservableCollection<TaskModel> _tasks;

        public MainWindow()
        {
            InitializeComponent();
            _taskService = new TaskService();
            _tasks = new ObservableCollection<TaskModel>();

            InitializeDataGrid();
            LoadTasks();
        }

        private void InitializeDataGrid()
        {
            // ربط البيانات بالجدول
            dgTasks.ItemsSource = _tasks;

            // أحداث الجدول
            dgTasks.SelectionChanged += DgTasks_SelectionChanged;
        }

        private async void LoadTasks()
        {
            try
            {
                txtStatus.Text = "جاري تحميل المهام...";
                var tasks = await _taskService.GetAllTasksAsync();

                _tasks.Clear();
                foreach (var task in tasks)
                {
                    _tasks.Add(task);
                }

                UpdateTaskCount();
                txtStatus.Text = "تم تحميل المهام بنجاح";
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في تحميل المهام: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                txtStatus.Text = "فشل في تحميل المهام";
            }
        }

        private void UpdateTaskCount()
        {
            txtTaskCount.Text = $"عدد المهام: {_tasks.Count}";
        }

        private async void BtnAddTask_Click(object sender, RoutedEventArgs e)
        {
            var addTaskWindow = new AddTaskWindow();
            if (addTaskWindow.ShowDialog() == true)
            {
                try
                {
                    var newTask = await _taskService.AddTaskAsync(addTaskWindow.Task);
                    _tasks.Add(newTask);
                    UpdateTaskCount();
                    txtStatus.Text = "تم إضافة المهمة بنجاح";
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"خطأ في إضافة المهمة: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            await Task.Run(() => LoadTasks());
        }

        private async void BtnSearch_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtSearch.Text) || txtSearch.Text == "البحث في المهام...")
            {
                LoadTasks();
                return;
            }

            try
            {
                txtStatus.Text = "جاري البحث...";
                var searchResults = await _taskService.SearchTasksAsync(txtSearch.Text);

                _tasks.Clear();
                foreach (var task in searchResults)
                {
                    _tasks.Add(task);
                }

                UpdateTaskCount();
                txtStatus.Text = $"تم العثور على {searchResults.Count} مهمة";
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CmbViewType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (schedulerControl == null) return;

            try
            {
                var selectedItem = cmbViewType.SelectedItem as ComboBoxItem;
                if (selectedItem?.Tag != null)
                {
                    // سيتم تنفيذ تغيير العرض لاحقاً
                    txtStatus.Text = $"تم تغيير العرض إلى: {selectedItem.Content}";
                }
            }
            catch (Exception ex)
            {
                txtStatus.Text = $"خطأ في تغيير العرض: {ex.Message}";
            }
        }

        private async void SchedulerControl_AppointmentAdded(object sender, EventArgs e)
        {
            // سيتم التعامل مع هذا لاحقاً
        }

        private async void SchedulerControl_AppointmentChanged(object sender, EventArgs e)
        {
            // سيتم التعامل مع هذا لاحقاً
        }

        private async void SchedulerControl_AppointmentDeleted(object sender, EventArgs e)
        {
            // سيتم التعامل مع هذا لاحقاً
        }

        protected override void OnClosed(EventArgs e)
        {
            _taskService?.Dispose();
            base.OnClosed(e);
        }
    }
}